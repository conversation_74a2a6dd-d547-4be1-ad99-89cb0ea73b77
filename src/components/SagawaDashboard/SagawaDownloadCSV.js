import React, { useState, useEffect, useContext, useMemo } from 'react'
import { Button, Checkbox, Col, Row, Form, Input, Select } from 'antd'
import { DownloadOutlined } from '@ant-design/icons'
import OrderService from '../../network/services/order'
import AppSettingService from '../../network/services/appSetting'
import { saveAs } from 'file-saver'
import Gaps from '../Gaps'
import { orderSelectContext } from '../../pages/Dashboard'
import { useTranslation } from 'react-i18next'
import dashboardOptionsStore from '../../lib/store/dashboard_options'
import { useSnapshot } from 'valtio'
import useSWR from 'swr'
import _ from 'lodash'

const SagawaDownloadCSV = ({ message }) => {
  const [form] = Form.useForm()
  const excludeSku = Form.useWatch('exclude_sku', form)
  const idColumn = Form.useWatch('id_column', form)
  // const generatePickingList = Form.useWatch('generate_picking_list', form)
  const [enableDefaultName, setEnableDefaultName] = useState(true)
  const [loading, setLoading] = useState(false)
  const { orderIds } = useContext(orderSelectContext)
  const { state } = useSnapshot(dashboardOptionsStore)
  const { data: appsetting } = useSWR(AppSettingService.get)
  const defaultItemName = useMemo(() => {
    if (appsetting) {
      return appsetting?.fulfillment_setting?.default_item_name ?? ''
    }
    return ''
  }, [appsetting])

  const { t } = useTranslation('translation', { keyPrefix: 'dashboard' })
  const { t: generalT } = useTranslation('translation')
  // const [startDate, setStartDate] = useState(moment().add(1, 'day').format('YYYY-MM-DD'))
  // const [endDate, setEndDate] = useState(moment().add(1, 'day').format('YYYY-MM-DD'))
  // const [ignore, setIgnore] = useState(false)
  // const [waitingOnly, setWaitingOnly] = useState(false)
  useEffect(() => {
    dashboardOptionsStore.state.id_column = idColumn
  }, [idColumn])

  useEffect(() => {
    dashboardOptionsStore.state.exclude_sku = excludeSku
  }, [excludeSku])

  useEffect(() => {
    var defaultName = ''
    if (enableDefaultName) {
      defaultName = defaultItemName
    }
    form.setFieldsValue({
      default_item_name: defaultName
    })
  }, [enableDefaultName, form, defaultItemName])

  useEffect(() => {
    if (orderIds.length > 0) {
      form.setFieldsValue({
        waiting_shipped_only: false
      })
      return
    }
    form.setFieldsValue({
      waiting_shipped_only: true
    })
  }, [orderIds, form])

  const handleDownload = async (values) => {
    setLoading(true)
    const data = {
      // start_date: startDate,
      // end_date: endDate,
      ...values,
      ids: orderIds,
      exclude_sku:
        values?.exclude_sku?.length > 0
          ? values.exclude_sku
              .split(',')
              .map((item) => _.trim(item))
              .filter((item) => !_.isEmpty(item))
          : undefined,
      include_sku:
        values?.include_sku?.length > 0
          ? values.include_sku
              .split(',')
              .map((item) => _.trim(item))
              .filter((item) => !_.isEmpty(item))
          : undefined
    }
    try {
      const { data: response } = await OrderService.downloadSagawaCSV(data)
      saveAs(response, 'orderCSV.zip')
    } catch (e) {
      const text = (await e.response?.data?.text()) ?? '{}'
      console.log({ data: JSON.parse(text), ...e })
      message.error(text)
    }
    setLoading(false)
  }

  // const handleStartDate = (date, dateString) => {
  //   console.log(dateString)
  //   setStartDate(dateString)
  // }

  // const handleEndDate = (date, dateString) => {
  //   setEndDate(dateString)
  // }

  return (
    <Form
      form={form}
      name="download_csv"
      layout="horizontal"
      onFinish={handleDownload}
      initialValues={{
        waiting_shipped_only: true,
        remove_jp_phone_code: true,
        generate_picking_list: true,
        sort_by: 'id',
        desc: false,
        id_column: 'id',
        full_exclude_sku: true,
        ...state
      }}
    >
      {/* <Row gutter={[12, 12]}>
        <Col>Start date</Col>
        <Col>
          <DatePicker defaultValue={moment().add(-1, 'day')} onChange={handleStartDate} />
        </Col>
      </Row>
      <Gaps />
      <Row gutter={[12, 12]}>
        <Col>End date</Col>
        <Col>
          <DatePicker defaultValue={moment()} onChange={handleEndDate} />
        </Col>
      </Row>
      <Gaps /> */}

      <Row>
        <Col>
          <Form.Item name="ignore_generated" valuePropName="checked">
            <Checkbox>{t('option1')}</Checkbox>
          </Form.Item>
        </Col>
      </Row>

      <Row>
        <Col>
          <Form.Item name="remove_jp_phone_code" valuePropName="checked">
            <Checkbox>{t('option2')}</Checkbox>
          </Form.Item>
        </Col>
      </Row>

      <Row>
        <Col>
          <Form.Item name="waiting_shipped_only" valuePropName="checked">
            <Checkbox>{t('option3')}</Checkbox>
          </Form.Item>
        </Col>
      </Row>

      <Row>
        <Col>
          <Form.Item name="generate_picking_list" valuePropName="checked">
            <Checkbox>{t('option4')}</Checkbox>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Form.Item name="sort_by" label={generalT('Sort by')}>
            <Select>
              <Select.Option value="id">ID</Select.Option>
              <Select.Option value="name">Order Name</Select.Option>
              <Select.Option value="product_name">Product Name</Select.Option>
              <Select.Option value="sku">SKU</Select.Option>
              <Select.Option value="platform_order_id">Platform Order ID</Select.Option>
              <Select.Option value="created_at">Created At</Select.Option>
            </Select>
          </Form.Item>
        </Col>

        <Col span={8}>
          <Form.Item name="desc" label={generalT('Direction')}>
            <Select>
              <Select.Option value={false}>{generalT('Ascending')}</Select.Option>
              <Select.Option value={true}>{generalT('Descending')}</Select.Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Form.Item name="id_column" label="お客様管理番号">
            <Select>
              <Select.Option value="id">Order ID</Select.Option>
              <Select.Option value="name">Order Name</Select.Option>
              <Select.Option value="platform_order_id">Platform Order ID</Select.Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Form.Item name="exclude_sku" label="CSVから除外したいSKUを指定する">
            <Input placeholder="sku_A,sku_B,sku_C" />
          </Form.Item>
        </Col>

        <Col span={8}>
          <Form.Item name="full_exclude_sku" valuePropName="checked">
            <Checkbox>指定されたSKUを含む注文を全部CSVから除外する</Checkbox>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Form.Item name="include_sku" label="出力したいSKUを指定する">
            <Input placeholder="sku_A,sku_B,sku_C" />
          </Form.Item>
        </Col>

        <Col span={8}>
          <Form.Item name="full_include_sku" valuePropName="checked">
            <Checkbox>指定されたSKUを含む注文を出力する</Checkbox>
          </Form.Item>
        </Col>
      </Row>

      <Row>
        <Col>
          <Checkbox
            checked={enableDefaultName}
            onClick={() => setEnableDefaultName(!enableDefaultName)}
          >
            {t('option5')}
          </Checkbox>
        </Col>
      </Row>

      {enableDefaultName && (
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Form.Item name="default_item_name" label={t('defaultItemName')}>
              <Input />
            </Form.Item>
          </Col>
        </Row>
      )}

      <Gaps />
      <Row gutter={[12, 12]}>
        <Col span={24} order="center">
          <Button
            type="primary"
            shape="round"
            icon={<DownloadOutlined />}
            size={'large'}
            onClick={() => form.submit()}
            loading={loading}
          >
            {generalT('Download')}
          </Button>
        </Col>
      </Row>
    </Form>
  )
}

export default SagawaDownloadCSV
