import client from '../request'

const getAll = `/reviews`

const get = (id) => {
  return `/reviews/${id}`
}

const create = (data) => {
  return client.post('/reviews', data)
}

const update = (id, data) => {
  return client.put(`/reviews/${id}`, data)
}

const toRow = (data) => {
  if (data?.data?.length > 0) {
    return data.data.map((element) => {
      return {
        ...element,
        product_name: element.product?.product_name,
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data) => {
  return {
    total: data?.meta?.total ?? 0
  }
}

// axios
const remove = (id) => {
  return client.delete(`/reviews/${id}`)
}

const ReviewService = {
  getAll,
  get,
  create,
  update,
  toRow,
  toPaginate,
  remove
}

export default ReviewService
