import client from '../request'

//get all
const getAll = '/shopify-settings'
//get all
const getShopifyLocations = '/shopify-locations'

//get by id
const get = (id) => {
  return `/shopify-settings/${id}`
}

// axios
const create = (data) => {
  return client.post('/shopify-settings', data)
}

// axios
const update = (id, data) => {
  return client.put(`/shopify-settings/${id}`, data)
}

// axios
const remove = (id) => {
  return client.delete(`/shopify-settings/${id}`)
}

const toRow = (data) => {
  if (data?.data?.length > 0) {
    return data.data.map((element) => {
      return {
        ...element,
        key: element.id,
        is_default: element.is_default ? 'Yes' : 'No'
      }
    })
  }

  return []
}

const toPaginate = (data) => {
  return {
    total: data?.meta?.total ?? 0
  }
}

const ShopifySettingService = {
  getAll,
  getShopifyLocations,
  get,
  create,
  update,
  remove,
  toRow,
  toPaginate
}

export default ShopifySettingService
