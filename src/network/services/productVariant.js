import client from '../request'

// useSWR, return strings
const getAll = '/variants'

// useSWR, return strings
const get = (id) => {
  return `/variants/${id}`
}

// axios
const update = (id, data) => {
  return client.put(`/variants/${id}`, data)
}

// processing
const toRow = (data) => {
  if (data?.data?.length > 0) {
    return data.data.map((element) => {
      return {
        ...element,
        key: element.id
      }
    })
  }

  return []
}

// processing
const toPaginate = (data) => {
  return {
    total: data?.meta?.total ?? 0
  }
}

// axios
const remove = (id) => {
  return client.delete(`/variants/${id}`)
}

const ProductVariantService = {
  getAll,
  get,
  update,
  toPaginate,
  toRow,
  remove
}

export default ProductVariantService
