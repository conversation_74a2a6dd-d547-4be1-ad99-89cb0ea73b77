import client from '../request'

//get all
const getAll = '/yamato-settings'

//get by id
const get = (id) => {
  return `/yamato-settings/${id}`
}

// axios
const create = (data) => {
  return client.post('/yamato-settings', data)
}

// axios
const update = (id, data) => {
  return client.put(`/yamato-settings/${id}`, data)
}

// axios
const remove = (id) => {
  return client.delete(`/yamato-settings/${id}`)
}

const toRow = (data) => {
  if (data?.data?.length > 0) {
    return data.data.map((element) => {
      return {
        ...element,
        key: element.id,
        is_default: element.is_default ? 'Yes' : 'No'
      }
    })
  }

  return []
}

const toPaginate = (data) => {
  return {
    total: data?.meta?.total ?? 0
  }
}

const YamatoSettingService = {
  getAll,
  get,
  create,
  update,
  remove,
  toRow,
  toPaginate
}

export default YamatoSettingService
