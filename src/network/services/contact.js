// useSWR, return strings
const getAll = '/contacts'

// useSWR, return strings
const get = (id) => {
  return `/contacts/${id}`
}

// processing
const toRow = (data) => {
  if (data?.data?.length > 0) {
    return data.data.map((element) => {
      return {
        ...element,
        key: element.id
      }
    })
  }

  return []
}

// processing
const toPaginate = (data) => {
  return {
    total: data?.meta?.total ?? 0
  }
}

const ContactService = {
  getAll,
  get,
  toPaginate,
  toRow
}

export default ContactService
