import client from '../request'

// useSWR, return strings
const getAll = '/return_status'

// useSWR, return strings
const get = (id) => {
  return `/return_status/${id}`
}

// axios
const create = (data) => {
  return client.post('/return_status', data)
}

// axios
const update = (id, data) => {
  return client.put(`/return_status/${id}`, data)
}

// processing
const toRow = (data) => {
  if (data?.data?.length > 0) {
    return data.data.map((element) => {
      return {
        ...element,
        stock_info: `[${element.event.stock.warehouse.warehouse_name}] [${element.event.stock.variant.product.product_name}]`,
        key: element.id
      }
    })
  }

  return []
}

// processing
const toPaginate = (data) => {
  return {
    total: data?.meta?.total ?? 0
  }
}

// axios
const remove = (id) => {
  return client.delete(`/return_status/${id}`)
}

const ReturnStatusService = {
  getAll,
  get,
  create,
  update,
  toPaginate,
  toRow,
  remove
}

export default ReturnStatusService
