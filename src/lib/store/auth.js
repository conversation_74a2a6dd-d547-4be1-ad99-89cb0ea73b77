import { proxy } from 'valtio'

const initialValue = {
  token: null,
  profile: null,
  sources: [],
  // super admin: 0, admin: 1, operator: 2
  acl: null
}

const authStore = proxy({
  state: initialValue,
  reset() {
    authStore.state = { ...initialValue }
  }
})

export const checkAuthorization = () => {
  const token = getToken()
  const profile = getProfile()
  if (token != null) {
    login({ token, profile })
    return true
  } else {
    console.log('not authenticated')
    return false
  }
}

export const login = ({ token, profile, acl, sources }) => {
  if (token) {
    authStore.state.token = token
    authStore.state.profile = profile
    authStore.state.acl = profile.admin?.type ?? 2
    authStore.state.sources = sources

    console.log(token)
    localStorage.setItem('token', token)
    localStorage.setItem('profile', JSON.stringify(profile))
  } else {
    // login error
  }
}

export const logout = () => {
  clearToken()
  clearProfile()

  authStore.state.token = null
  authStore.state.profile = null
}

export function getToken() {
  try {
    const token = localStorage.getItem('token')
    return token
  } catch (err) {
    clearToken()
    return null
  }
}

export function getProfile() {
  try {
    const profile = localStorage.getItem('profile')
    return JSON.parse(profile)
  } catch (err) {
    clearProfile()
    return null
  }
}

export function clearToken() {
  localStorage.removeItem('token')
}

export function clearProfile() {
  localStorage.removeItem('profile')
}

export default authStore
