import React from 'react'
import { useNavigate } from 'react-router-dom'
import { Card, PageHeader, Button, Empty, Spin } from 'antd'
import useSWR from 'swr'
import { useSnapshot } from 'valtio'

import tableStore from '../../lib/store/blacklist_table'
import { serialize } from '../../network/request'
import BlacklistService from '../../network/services/blacklist'
import BlacklistTable from './BlacklistTable'

const BlacklistPage = () => {
  const navigate = useNavigate()
  const primaryAction = (
    <Button
      key="add"
      type="primary"
      onClick={() => {
        navigate('new')
      }}
    >
      Create Blacklist
    </Button>
  )

  return (
    <PageHeader title="All Blacklist" extra={[primaryAction]}>
      <Card>
        <BlacklistTableWrapper />
      </Card>
    </PageHeader>
  )
}

const BlacklistTableWrapper = () => {
  const { state } = useSnapshot(tableStore)

  // use this to preload
  const { data: response, error } = useSWR(
    serialize(BlacklistService.getAll, { page: 1, limit: state.pageSize })
  )

  if (error) {
    console.log(error)
    return <Empty description={error?.message ?? 'Please try again later'} />
  }

  if (!response) {
    return <Spin />
  }

  const { total } = BlacklistService.toPaginate(response)

  return (
    <>
      <BlacklistTable total={total} />
      {/* preload next page */}
      {state.currentPage * state.pageSize < total && (
        <div style={{ display: 'none' }}>
          <BlacklistTable total={total} overridePage={state.currentPage + 1} />
        </div>
      )}
    </>
  )
}

export default BlacklistPage
