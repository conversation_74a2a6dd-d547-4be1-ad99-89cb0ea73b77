import React from 'react'
import { useNavigate } from 'react-router-dom'
import { Card, PageHeader, Button, Empty, Spin } from 'antd'
import useSWR from 'swr'
import { useSnapshot } from 'valtio'

import tableStore from '../../lib/store/yamato_setting'
import YamatoSettingService from '../../network/services/yamatoSetting'
import { serialize } from '../../network/request'
import YamatoSettingTable from './ShopifySettingTable'

const ShopifySettingPage = () => {
  const navigate = useNavigate()
  const primaryAction = (
    <Button
      key="add"
      type="primary"
      onClick={() => {
        navigate(`/dashboard/shopify-setting/new`)
      }}
    >
      Create Yamato Setting
    </Button>
  )

  return (
    <PageHeader title="All Shopify Setting" extra={[primaryAction]}>
      <Card>
        <EventTableWrapper />
      </Card>
    </PageHeader>
  )
}

const EventTableWrapper = () => {
  const { state } = useSnapshot(tableStore)

  // use this to preload
  const { data: response, error } = useSWR(
    serialize(YamatoSettingService.getAll, { page: 1, limit: state.pageSize })
  )

  if (error) {
    console.log(error)
    return <Empty description={error?.message ?? 'Please try again later'} />
  }

  if (!response) {
    return <Spin />
  }

  const { total } = YamatoSettingService.toPaginate(response)

  return (
    <>
      <YamatoSettingTable total={total} />
      {/* preload next page */}
      {state.currentPage * state.pageSize < total && (
        <div style={{ display: 'none' }}>
          <YamatoSettingTable total={total} overridePage={state.currentPage + 1} />
        </div>
      )}
    </>
  )
}

export default ShopifySettingPage
