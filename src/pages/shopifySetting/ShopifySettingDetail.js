import React from 'react'
import { useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { Form, Empty, Spin, PageHeader, Button } from 'antd'
import useSWR from 'swr'

import { serialize } from '../../network/request'
import ShopifySettingForm from './ShopifySettingForm'
import ShopifySettingService from '../../network/services/shopifySetting'

const ShopifySettingDetail = () => {
  const navigate = useNavigate()
  const { id } = useParams()

  const [form] = Form.useForm()
  const [isLoading, setIsLoading] = useState(false)

  const saveAction = (
    <Button
      key={'save'}
      type="primary"
      onClick={() => {
        form.submit()
      }}
      loading={isLoading}
    >
      Save
    </Button>
  )
  const discardAction = (
    <Button
      key={'discard'}
      type="ghost"
      onClick={() => {
        navigate('/dashboard/setting_dashboard')
      }}
    >
      Discard
    </Button>
  )

  // use this to preload
  const {
    data: response,
    error,
    mutate
  } = useSWR(serialize(id !== 'new' ? ShopifySettingService.get(id) : null))
  const { data: Shopifylocations } = useSWR(ShopifySettingService.getShopifyLocations)

  if (error) {
    console.log(error)
    return <Empty description={error?.message ?? 'Please try again later'} />
  }

  if (!response && id !== 'new') {
    return (
      <PageHeader>
        <Spin />
      </PageHeader>
    )
  }

  const refresh = () => {
    if (id != null) {
      if (id === 'new') {
      } else {
        mutate()
      }
    }
  }

  console.log(response)

  return (
    <PageHeader
      title={id === 'new' ? 'New Shopify Setting' : 'Edit Shopify Setting'}
      extra={[discardAction, saveAction]}
      onBack={() => navigate('/dashboard/setting_dashboard')}
    >
      <ShopifySettingForm
        form={form}
        initialValue={response}
        shopifyLocations={Shopifylocations}
        isLoading={isLoading}
        setIsLoading={setIsLoading}
        refresh={refresh}
        updateAllowed={true}
        createAllowed={true}
        readAllowed={true}
        deleteAllowed={true}
      />
    </PageHeader>
  )
}

export default ShopifySettingDetail
