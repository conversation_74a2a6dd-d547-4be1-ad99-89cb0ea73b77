import React from 'react'
import { <PERSON>, PageHeader, Empty, Spin } from 'antd'
import useSWR from 'swr'
import { useSnapshot } from 'valtio'

import tableStore from '../../lib/store/contact_table'
import ContactService from '../../network/services/contact'
import { serialize } from '../../network/request'
import ContactTable from './ContactTable'

const ContactPage = () => {
  return (
    <PageHeader title="All Contact">
      <Card>
        <ContactTableWrapper />
      </Card>
    </PageHeader>
  )
}

const ContactTableWrapper = () => {
  const { state } = useSnapshot(tableStore)

  // use this to preload
  const { data: response, error } = useSWR(
    serialize(ContactService.getAll, { page: 1, limit: state.pageSize })
  )

  if (error) {
    console.log(error)
    return <Empty description={error?.message ?? 'Please try again later'} />
  }

  if (!response) {
    return <Spin />
  }

  const { total } = ContactService.toPaginate(response)

  return (
    <>
      <ContactTable total={total} />
      {/* preload next page */}
      {state.currentPage * state.pageSize < total && (
        <div style={{ display: 'none' }}>
          <ContactTable total={total} overridePage={state.currentPage + 1} />
        </div>
      )}
    </>
  )
}

export default ContactPage
