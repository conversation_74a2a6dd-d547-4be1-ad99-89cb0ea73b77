import React from 'react'
import { useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { Form, Empty, Spin, PageHeader } from 'antd'
import useSWR from 'swr'

import { serialize } from '../../network/request'
import ContactService from '../../network/services/contact'
import ContactForm from './ContactForm'

const ContactDetail = () => {
  const navigate = useNavigate()
  const { id } = useParams()

  const [form] = Form.useForm()
  const [isLoading, setIsLoading] = useState(false)

  // use this to preload
  const {
    data: response,
    error,
    mutate
  } = useSWR(serialize(id !== 'new' ? ContactService.get(id) : null))

  if (error) {
    console.log(error)
    return <Empty description={error?.message ?? 'Please try again later'} />
  }

  if (!response && id !== 'new') {
    return (
      <PageHeader>
        <Spin />
      </PageHeader>
    )
  }

  const refresh = () => {
    if (id != null) {
      if (id === 'new') {
      } else {
        mutate()
      }
    }
  }

  return (
    <PageHeader title={'View Contact Detail'} onBack={() => navigate('/dashboard/contact')}>
      <ContactForm
        form={form}
        initialValue={response}
        isLoading={isLoading}
        setIsLoading={setIsLoading}
        refresh={refresh}
        readAllowed={true}
      />
    </PageHeader>
  )
}

export default ContactDetail
