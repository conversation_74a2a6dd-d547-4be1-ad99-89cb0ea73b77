import React from 'react'
import { useNavigate } from 'react-router-dom'
import { Card, PageHeader, Button, Empty, Spin } from 'antd'
import useSWR from 'swr'
import { useSnapshot } from 'valtio'

import tableStore from '../../lib/store/stock_table'
import StockService from '../../network/services/stock'
import { serialize } from '../../network/request'
import StockTable from './StockTable'

const StockPage = () => {
  const navigate = useNavigate()
  const primaryAction = (
    <Button
      key="add"
      type="primary"
      onClick={() => {
        navigate('new')
      }}
    >
      Create Stock
    </Button>
  )

  return (
    <PageHeader title="All Stock" extra={[primaryAction]}>
      <Card>
        <EventTableWrapper />
      </Card>
    </PageHeader>
  )
}

const EventTableWrapper = () => {
  const { state } = useSnapshot(tableStore)

  // use this to preload
  const { data: response, error } = useSWR(serialize('/stocks', { page: 1, limit: state.pageSize }))

  if (error) {
    console.log(error)
    return <Empty description={error?.message ?? 'Please try again later'} />
  }

  if (!response) {
    return <Spin />
  }

  const { total } = StockService.toPaginate(response)

  return (
    <>
      <StockTable total={total} />
      {/* preload next page */}
      {state.currentPage * state.pageSize < total && (
        <div style={{ display: 'none' }}>
          <StockTable total={total} overridePage={state.currentPage + 1} />
        </div>
      )}
    </>
  )
}

export default StockPage
