import React from 'react'
import { useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { Form, Empty, Spin, PageHeader, Button } from 'antd'
import useSWR from 'swr'

import { serialize } from '../../network/request'
import StockService from '../../network/services/stock'
import WarehouseService from '../../network/services/warehouse'
import StockForm from './StockForm'
import ProductVariantService from '../../network/services/productVariant'
const shiftCharCode = (Δ) => (c) => String.fromCharCode(c.charCodeAt(0) + Δ)
const toHalfWidth = (str) => str?.replace(/[！-～]/g, shiftCharCode(-0xfee0))

const StockDetail = () => {
  const navigate = useNavigate()
  const { id } = useParams()

  const [form] = Form.useForm()
  const [isLoading, setIsLoading] = useState(false)
  const [variantSearch, setVariantSearch] = useState('')
  const [delaySearch, setDelaySearch] = useState()

  const saveAction = (
    <Button
      key={'save'}
      type="primary"
      onClick={() => {
        form.submit()
      }}
      loading={isLoading}
    >
      Save
    </Button>
  )
  const discardAction = (
    <Button
      key={'discard'}
      type="ghost"
      onClick={() => {
        navigate('/dashboard/stocks')
      }}
    >
      Discard
    </Button>
  )

  const handleVariantSearch = (value) => {
    clearTimeout(delaySearch)
    const asciiValue = value?.length > 0 ? toHalfWidth(value) : ''
    const deplayDebounce = setTimeout(() => {
      setVariantSearch(asciiValue)
    }, 500)
    setDelaySearch(deplayDebounce)
  }

  // use this to preload
  const {
    data: response,
    error,
    mutate
  } = useSWR(serialize(id !== 'new' ? StockService.get(id) : null))

  //preload the dropdown menu content
  const { data: variants } = useSWR(
    serialize(ProductVariantService.getAll, {
      search: variantSearch
    })
  )
  const { data: warehouses } = useSWR(serialize(WarehouseService.getAll))
  const { data: qualities } = useSWR(StockService.getQualities)

  const variantRows = ProductVariantService.toRow(variants)
  const warehouseRows = WarehouseService.toRow(warehouses)

  if (error) {
    console.log(error)
    return <Empty description={error?.message ?? 'Please try again later'} />
  }

  if (!response && id !== 'new') {
    return (
      <PageHeader>
        <Spin />
      </PageHeader>
    )
  }

  const refresh = () => {
    if (id != null) {
      if (id === 'new') {
      } else {
        mutate()
      }
    }
  }

  return (
    <PageHeader
      title={id === 'new' ? 'New Stock' : 'Edit Stock'}
      extra={[discardAction, saveAction]}
      onBack={() => navigate('/dashboard/stocks')}
    >
      <StockForm
        form={form}
        initialValue={response}
        isLoading={isLoading}
        setIsLoading={setIsLoading}
        refresh={refresh}
        warehouses={warehouseRows}
        qualities={qualities}
        variants={variantRows}
        handleVariantSearch={handleVariantSearch}
        updateAllowed={true}
        createAllowed={true}
        readAllowed={true}
        deleteAllowed={true}
      />
    </PageHeader>
  )
}

export default StockDetail
