import React from 'react'
import { useNavigate } from 'react-router-dom'
import { Card, PageHeader, Button, Empty, Spin } from 'antd'
import useSWR from 'swr'
import { useSnapshot } from 'valtio'

import tableStore from '../../lib/store/fulfillment_setting'
import FulfillmentSettingService from '../../network/services/fulfillmentSetting'
import { serialize } from '../../network/request'
import FulfillmentSettingTable from './FulfillmentSettingTable'

const FulfillmentSettingPage = () => {
  const navigate = useNavigate()
  const primaryAction = (
    <Button
      key="add"
      type="primary"
      onClick={() => {
        navigate(`/dashboard/fulfillment_setting/new`)
      }}
    >
      Create Fulfillment Setting
    </Button>
  )

  return (
    <PageHeader title="All Fulfillment Setting" extra={[primaryAction]}>
      <Card>
        <EventTableWrapper />
      </Card>
    </PageHeader>
  )
}

const EventTableWrapper = () => {
  const { state } = useSnapshot(tableStore)

  // use this to preload
  const { data: response, error } = useSWR(
    serialize(FulfillmentSettingService.getAll, { page: 1, limit: state.pageSize })
  )

  if (error) {
    console.log(error)
    return <Empty description={error?.message ?? 'Please try again later'} />
  }

  if (!response) {
    return <Spin />
  }

  const { total } = FulfillmentSettingService.toPaginate(response)

  return (
    <>
      <FulfillmentSettingTable total={total} />
      {/* preload next page */}
      {state.currentPage * state.pageSize < total && (
        <div style={{ display: 'none' }}>
          <FulfillmentSettingTable total={total} overridePage={state.currentPage + 1} />
        </div>
      )}
    </>
  )
}

export default FulfillmentSettingPage
