import React from 'react'
import { useNavigate } from 'react-router-dom'
import { Card, PageHeader, Button, Empty, Spin } from 'antd'
import useSWR from 'swr'
import { useSnapshot } from 'valtio'

import tableStore from '../../lib/store/return_status_table'
import ReturnStatusService from '../../network/services/returnStatus'
import { serialize } from '../../network/request'
import ReturnStatusTable from './ReturnStatusTable'

const ReturnStatusPage = () => {
  const navigate = useNavigate()
  const primaryAction = (
    <Button
      key="add"
      type="primary"
      onClick={() => {
        navigate('new')
      }}
    >
      Create Return Status
    </Button>
  )

  return (
    <PageHeader title="All Return Status" extra={[primaryAction]}>
      <Card>
        <ReturnStatusTableWrapper />
      </Card>
    </PageHeader>
  )
}

const ReturnStatusTableWrapper = () => {
  const { state } = useSnapshot(tableStore)

  // use this to preload
  const { data: response, error } = useSWR(
    serialize('/return_status', { page: 1, limit: state.pageSize })
  )

  if (error) {
    console.log(error)
    return <Empty description={error?.message ?? 'Please try again later'} />
  }

  if (!response) {
    return <Spin />
  }

  const { total } = ReturnStatusService.toPaginate(response)

  return (
    <>
      <ReturnStatusTable total={total} />
      {/* preload next page */}
      {state.currentPage * state.pageSize < total && (
        <div style={{ display: 'none' }}>
          <ReturnStatusTable total={total} overridePage={state.currentPage + 1} />
        </div>
      )}
    </>
  )
}

export default ReturnStatusPage
