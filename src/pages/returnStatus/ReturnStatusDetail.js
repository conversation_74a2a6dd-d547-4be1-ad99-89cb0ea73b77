import React from 'react'
import { useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { Form, Empty, Spin, PageHeader, Button } from 'antd'
import useSWR from 'swr'

import { serialize } from '../../network/request'
import ReturnStatusService from '../../network/services/returnStatus'
import ReturnStatusForm from './ReturnStatusForm'
import StockService from '../../network/services/stock'
import SourceService from '../../network/services/source'

const ReturnStatusDetail = () => {
  const navigate = useNavigate()
  const { id } = useParams()

  const [form] = Form.useForm()
  const [isLoading, setIsLoading] = useState(false)

  const saveAction = (
    <Button
      key={'save'}
      type="primary"
      onClick={() => {
        form.submit()
      }}
      loading={isLoading}
    >
      Save
    </Button>
  )
  const discardAction = (
    <Button
      key={'discard'}
      type="ghost"
      onClick={() => {
        navigate('/dashboard/returnStatus')
      }}
    >
      Discard
    </Button>
  )

  // use this to preload
  const {
    data: response,
    error,
    mutate
  } = useSWR(serialize(id !== 'new' ? ReturnStatusService.get(id) : null))

  const reorderedResponse =
    id !== 'new'
      ? {
          ...response,
          stock_id: response?.event.stock_id,
          source_id: response?.event.source_id,
          quantity: response?.event.quantity
        }
      : null

  //preload the dropdown menu content
  const { data: stocks } = useSWR(serialize(StockService.getAll))
  const stockRows = StockService.toRow(stocks)

  const { data: sources } = useSWR(serialize(SourceService.getAll))
  const sourceRows = SourceService.toRow(sources)

  if (error) {
    console.log(error)
    return <Empty description={error?.message ?? 'Please try again later'} />
  }

  if (!response && id !== 'new') {
    return (
      <PageHeader>
        <Spin />
      </PageHeader>
    )
  }

  const refresh = () => {
    if (id != null) {
      if (id === 'new') {
      } else {
        mutate()
      }
    }
  }

  return (
    <PageHeader
      title={id === 'new' ? 'New Return Status' : 'Edit Return Status'}
      extra={[discardAction, saveAction]}
      onBack={() => navigate('/dashboard/returnStatus')}
    >
      <ReturnStatusForm
        form={form}
        initialValue={reorderedResponse}
        isLoading={isLoading}
        setIsLoading={setIsLoading}
        refresh={refresh}
        stocks={stockRows}
        sources={sourceRows}
        updateAllowed={true}
        createAllowed={true}
        readAllowed={true}
        deleteAllowed={true}
      />
    </PageHeader>
  )
}

export default ReturnStatusDetail
