import React from 'react'
import { useNavigate } from 'react-router-dom'
import { Card, PageHeader, Button, Empty, Spin } from 'antd'
import useSWR from 'swr'
import { useSnapshot } from 'valtio'

import tableStore from '../../lib/store/event_table'
import EventService from '../../network/services/event'
import { serialize } from '../../network/request'
import EventTable from './EventTable'

const EventPage = () => {
  const navigate = useNavigate()
  const primaryAction = (
    <Button
      key="add"
      type="primary"
      onClick={() => {
        navigate('new')
      }}
    >
      Create Event
    </Button>
  )

  return (
    <PageHeader title="All Events" extra={[primaryAction]}>
      <Card>
        <EventTableWrapper />
      </Card>
    </PageHeader>
  )
}

const EventTableWrapper = () => {
  const { state } = useSnapshot(tableStore)

  // use this to preload
  const { data: response, error } = useSWR(serialize('/events', { page: 1, limit: state.pageSize }))

  if (error) {
    console.log(error)
    return <Empty description={error?.message ?? 'Please try again later'} />
  }

  if (!response) {
    return <Spin />
  }

  const { total } = EventService.toPaginate(response)

  return (
    <>
      <EventTable total={total} />
      {/* preload next page */}
      {state.currentPage * state.pageSize < total && (
        <div style={{ display: 'none' }}>
          <EventTable total={total} overridePage={state.currentPage + 1} />
        </div>
      )}
    </>
  )
}

export default EventPage
