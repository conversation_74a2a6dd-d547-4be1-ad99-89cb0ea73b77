import React from 'react'
import { Card, PageHeader, Empty, Spin } from 'antd'
import useSWR from 'swr'
import { useSnapshot } from 'valtio'

import tableStore from '../../lib/store/recruitment_table'
import RecruitmentService from '../../network/services/recruitment'
import { serialize } from '../../network/request'
import RecruitmentTable from './RecruitmentTable'

const RecruitmentPage = () => {
  return (
    <PageHeader title="All Recruitment">
      <Card>
        <RecruitmentTableWrapper />
      </Card>
    </PageHeader>
  )
}

const RecruitmentTableWrapper = () => {
  const { state } = useSnapshot(tableStore)

  // use this to preload
  const { data: response, error } = useSWR(
    serialize(RecruitmentService.getAll, { page: 1, limit: state.pageSize })
  )

  if (error) {
    console.log(error)
    return <Empty description={error?.message ?? 'Please try again later'} />
  }

  if (!response) {
    return <Spin />
  }

  const { total } = RecruitmentService.toPaginate(response)

  return (
    <>
      <RecruitmentTable total={total} />
      {/* preload next page */}
      {state.currentPage * state.pageSize < total && (
        <div style={{ display: 'none' }}>
          <RecruitmentTable total={total} overridePage={state.currentPage + 1} />
        </div>
      )}
    </>
  )
}

export default RecruitmentPage
