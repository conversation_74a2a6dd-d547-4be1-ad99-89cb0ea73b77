import React from 'react'
import { useNavigate } from 'react-router-dom'
import { Card, PageHeader, Button, Empty, Spin } from 'antd'
import useSWR from 'swr'
import { useSnapshot } from 'valtio'

import tableStore from '../../lib/store/review_table'
import ReviewService from '../../network/services/review'
import { serialize } from '../../network/request'
import ReviewTable from './ReviewTable'

const ReviewPage = () => {
  const navigate = useNavigate()
  const primaryAction = (
    <Button
      key="add"
      type="primary"
      onClick={() => {
        navigate('new')
      }}
    >
      Create Review
    </Button>
  )

  return (
    <PageHeader title="All Reviews" extra={[primaryAction]}>
      <Card>
        <ReviewTableWrapper />
      </Card>
    </PageHeader>
  )
}

const ReviewTableWrapper = () => {
  const { state } = useSnapshot(tableStore)

  // use this to preload
  const { data: response, error } = useSWR(
    serialize(ReviewService.getAll, { page: 1, limit: state.pageSize })
  )

  if (error) {
    console.log(error)
    return <Empty description={error?.message ?? 'Please try again later'} />
  }

  if (!response) {
    return <Spin />
  }

  const { total } = ReviewService.toPaginate(response)

  return (
    <>
      <ReviewTable total={total} />
      {/* preload next page */}
      {state.currentPage * state.pageSize < total && (
        <div style={{ display: 'none' }}>
          <ReviewTable total={total} overridePage={state.currentPage + 1} />
        </div>
      )}
    </>
  )
}

export default ReviewPage
