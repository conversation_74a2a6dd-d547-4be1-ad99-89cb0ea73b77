import { useParams } from 'react-router-dom'
import { useState, useEffect, useMemo, useRef } from 'react'
import useSWR from 'swr'
import { serialize } from '../../network/request'
import OrderService from '../../network/services/order'
import { red, green, grey } from '@ant-design/colors'
import {
  Form,
  Empty,
  Spin,
  PageHeader,
  Button,
  Row,
  Col,
  Card,
  Input,
  Checkbox,
  Space,
  message,
  List
} from 'antd'
import { InfoCircleOutlined, CheckCircleOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import Gaps from '../../components/Gaps'
const shiftCharCode = (Δ) => (c) => String.fromCharCode(c.charCodeAt(0) + Δ)
const toHalfWidth = (str) => str.replace(/[！-～]/g, shiftCharCode(-0xfee0))

const OrderScanning = () => {
  const { tracking } = useParams()
  const asciiTracking = toHalfWidth(tracking)

  const { data: response, error } = useSWR(
    serialize(OrderService.getAll, {
      tracking: asciiTracking
    })
  )
  const order = OrderService.toRow(response)[0]
  const navigate = useNavigate()

  if (!response) {
    return (
      <PageHeader title={'Order Detail'} onBack={() => navigate('/scanner')}>
        <Spin />
      </PageHeader>
    )
  }

  if (error || !order) {
    return (
      <PageHeader title={'Order Detail'} onBack={() => navigate('/scanner')}>
        <Empty description={'No such order'} />
      </PageHeader>
    )
  }

  // if (response && order?.fulfillment_status_id === 5) {
  //   return (
  //     <PageHeader title={'Order Detail'} onBack={() => navigate('/scanner')}>
  //       <Empty description={'This order has already been fulfilled'} />
  //     </PageHeader>
  //   )
  // }

  return (
    <PageHeader title={'Order Detail'} onBack={() => navigate('/scanner')}>
      {order?.fulfillment_status_id === 5 && (
        <h2 style={{ color: green.primary }}>Order was successfully fulfilled</h2>
      )}
      {order?.fulfillment_status_id === 6 && <h2>Order was cancelled</h2>}
      <ProductWarpper order={order} />
    </PageHeader>
  )
}

const ProductWarpper = ({ order }) => {
  const { data: products } = useSWR(serialize(OrderService.getProducts(order.id)))

  if (!products) {
    return <Spin />
  }

  return <PageWrapper products={products} />
}

const PageWrapper = ({ products }) => {
  const [form] = Form.useForm()
  const navigate = useNavigate()
  const errorAudio = new Audio('/audio/error.mp3')
  const successAudio = new Audio('/audio/success.mp3')
  const barcodeInputRef = useRef(null)
  const [loading, setLoading] = useState(false)
  const [checkMessage, setCheckMessage] = useState(false)
  const [delayDebounce, setDelayDebounce] = useState()
  const [productChecklist, setProductChecklist] = useState([])
  const [disable, setDisable] = useState(true)
  const [notifyCustomer, setNotifyCustomer] = useState(true)
  const [errorMessage, setErrorMessage] = useState('')
  const [barcode, setBarcode] = useState('')
  const { tracking } = useParams()
  const asciiTracking = toHalfWidth(tracking)

  const { data: response, mutate } = useSWR(
    serialize(OrderService.getAll, {
      tracking: asciiTracking
    })
  )

  const order = OrderService.toRow(response)[0]
  const isFulfilled = useMemo(() => order?.fulfillment_status_id === 5, [order])

  const handleCheckProcuct = (barcode) => {
    setErrorMessage('')
    const asciiBarcode = toHalfWidth(barcode)
    const index = productChecklist.findIndex((item) => item.barcode === asciiBarcode)
    if (index !== -1) {
      const newList = [...productChecklist]
      if (newList[index].currentQty + 1 <= newList[index].quantity) {
        newList[index].currentQty += 1
        setProductChecklist([...newList])
        successAudio.play()
        return
      }
      setErrorMessage('Maximun amount reached')
      errorAudio.play()
      return
    }
    errorAudio.play()
    setErrorMessage('Scanned barcode does not match any product in the list')
  }

  const handleMarkAsScanned = (barcode) => {
    const index = productChecklist.findIndex((item) => item.barcode === barcode)
    if (index !== -1) {
      const newList = [...productChecklist]
      newList[index].currentQty = newList[index].quantity
      setProductChecklist([...newList])
    }
  }

  const handleInput = (value) => {
    clearTimeout(delayDebounce)
    if (value) {
      const deplayDebounce = setTimeout(() => {
        const asciiValue = toHalfWidth(value)
        handleCheckProcuct(asciiValue)
        setBarcode('')
      }, 500)
      setDelayDebounce(deplayDebounce)
    }
  }

  const handleFulfillment = async () => {
    if (disable || !checkMessage) return
    setLoading(true)
    try {
      const { data: result } = await OrderService.orderFulfillment(order.id, {
        notify_customer: notifyCustomer
      })
      if (result.success) {
        message.success('Order was successfully fulfilled')
        mutate()
        setTimeout(() => {
          navigate('/scanner/handheld')
        }, 400)
      }
    } catch (error) {
      mutate()
      message.error('Order fulfillment error ' + error.message)
    }
    setLoading(false)
  }

  useEffect(() => {
    const focusInput = () => barcodeInputRef?.current && barcodeInputRef?.current?.focus()
    const handleEnter = (e) => e.key === 'Enter' && handleFulfillment()

    focusInput()

    window.addEventListener('click', focusInput)
    window.addEventListener('keydown', handleEnter)

    return () => {
      window.removeEventListener('click', focusInput)
      window.removeEventListener('keydown', handleEnter)
    }
  })

  useEffect(() => {
    const checklist = products.map((item) => {
      return {
        ...item,
        quantity: Math.abs(item.quantity),
        currentQty: 0
      }
    })
    setProductChecklist([...checklist])
  }, [products])

  useEffect(() => {
    setCheckMessage(!order.customer_memo && !order.operator_memo)
  }, [order.customer_memo, order.operator_memo])

  useEffect(() => {
    if (productChecklist?.length > 0) {
      var allChecked = true
      for (var product of productChecklist) {
        if (product.quantity !== product.currentQty) {
          allChecked = false
          break
        }
      }
      setDisable(!allChecked)
      return
    }
    setDisable(true)
  }, [productChecklist])

  return (
    <Form
      form={form}
      initialValues={{
        ...order,
        customer_email: order.customer.email,
        shop_platform: order?.shop_platform
      }}
    >
      <Row gutter={[24, 24]}>
        <Col span={8}>
          <Card
            title={`Order #${order?.id}${order?.name ? ' / ' : ''}${order?.name}${
              order?.platform_order_id ? ' / ' : ''
            }${order?.platform_order_id}`}
          >
            <Form.Item name="shop_platform" label="Shop Platform">
              <Input readOnly bordered={false} style={{ fontWeight: 'bold' }} />
            </Form.Item>
            <Form.Item name="customer_name" label="Customer Name">
              <Input readOnly bordered={false} style={{ fontWeight: 'bold' }} />
            </Form.Item>
            <Form.Item name="customer_email" label="Customer Email">
              <Input readOnly bordered={false} style={{ fontWeight: 'bold' }} />
            </Form.Item>
            <Form.Item name="zip" label="ZIP">
              <Input readOnly bordered={false} style={{ fontWeight: 'bold' }} />
            </Form.Item>
            <Form.Item name="address" label="Address">
              <Input readOnly bordered={false} style={{ fontWeight: 'bold' }} />
            </Form.Item>
            <Form.Item name="appartment" label="Appartment">
              <Input readOnly bordered={false} style={{ fontWeight: 'bold' }} />
            </Form.Item>
            <Form.Item name="company" label="Company">
              <Input readOnly bordered={false} style={{ fontWeight: 'bold' }} />
            </Form.Item>
            <Form.Item name="tracking" label="Tracking Number">
              <Input readOnly bordered={false} style={{ fontWeight: 'bold' }} />
            </Form.Item>
            <Form.Item name="tracking_company_name" label="Tracking company">
              <Input readOnly bordered={false} style={{ fontWeight: 'bold' }} />
            </Form.Item>
            <Form.Item name="transaction_status" label="Transaction status">
              <Input readOnly bordered={false} style={{ fontWeight: 'bold' }} />
            </Form.Item>
            <Form.Item name="fulfillment_status" label="Fulfillment status">
              <Input readOnly bordered={false} style={{ fontWeight: 'bold' }} />
            </Form.Item>
          </Card>
        </Col>

        <Col span={16}>
          <Card
            bordered={false}
            title={isFulfilled ? 'This Order Has Already Been Fulfilled' : 'Products Ordered'}
          >
            {!isFulfilled && (
              <Input
                value={barcode}
                ref={barcodeInputRef}
                placeholder="Scan a product barcode"
                onChange={(e) => {
                  setBarcode(e.target.value)
                  handleInput(e.target.value)
                }}
                style={{ opacity: 1 }}
              />
            )}
            <p style={{ color: red.primary }}>{errorMessage}</p>
            <Gaps />

            <List
              grid={{ gutter: 16, column: 2 }}
              dataSource={productChecklist}
              renderItem={(product) => (
                <List.Item>
                  <ProductListItem
                    key={product.id}
                    product={product}
                    handleMarkAsScanned={handleMarkAsScanned}
                    fulfilled={isFulfilled}
                  />
                </List.Item>
              )}
            />

            <Row>
              <Col span={16}>
                <Card bordered={false} title={'Message'}>
                  <Form.Item name="customer_memo" label="Customer Memo">
                    <Input.TextArea rows={4} readOnly />
                  </Form.Item>
                  <Form.Item name="operator_memo" label="Operator Memo">
                    <Input.TextArea rows={4} readOnly />
                  </Form.Item>
                </Card>
                {!isFulfilled && (
                  <>
                    <Row>
                      <Col>
                        <Checkbox
                          checked={checkMessage}
                          onClick={() => setCheckMessage(!checkMessage)}
                        >
                          I have read the message above
                        </Checkbox>
                      </Col>
                    </Row>
                    <Row>
                      <Col>
                        <Checkbox
                          onClick={() => setNotifyCustomer(!notifyCustomer)}
                          checked={notifyCustomer}
                        >
                          Notify Customer
                        </Checkbox>
                      </Col>
                    </Row>
                    <Row>
                      <Col>
                        <Button
                          disabled={disable || !checkMessage}
                          loading={loading}
                          onClick={handleFulfillment}
                        >
                          Confirm Shippment
                        </Button>
                      </Col>
                    </Row>
                  </>
                )}
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </Form>
  )
}

const ProductListItem = ({ product, handleMarkAsScanned, fulfilled }) => {
  const scanSuccess = useMemo(() => {
    return product.currentQty === product.quantity
  }, [product.currentQty, product.quantity])
  return (
    <div id={product.key}>
      <Card
        style={{
          backgroundColor: fulfilled ? '#f0f0f0' : scanSuccess ? green[1] : red[1]
        }}
      >
        <p>
          {product.name} X {product.quantity}
        </p>
        <p>SKU : {product.sku}</p>
        <p>Barcode : {product.barcode}</p>
        <Gaps />

        {!fulfilled && (
          <Row>
            <Col>
              <Space style={{ color: scanSuccess ? '#1DA57A' : red.primary }}>
                {product.currentQty} / {product.quantity} scanned
              </Space>
            </Col>
          </Row>
        )}

        {!fulfilled &&
          (!scanSuccess ? (
            <Space style={{ color: red.primary }}>
              <InfoCircleOutlined />
              Scan to Match
              <Button onClick={() => handleMarkAsScanned(product.barcode)} disabled={scanSuccess}>
                Mark as scanned
              </Button>
            </Space>
          ) : (
            // TODO: use dynamic color
            <Space style={{ color: '#1DA57A' }}>
              <CheckCircleOutlined />
              Success
            </Space>
          ))}

        {fulfilled && (
          <Space style={{ color: grey[0] }}>
            <CheckCircleOutlined />
            Fulfilled
          </Space>
        )}
      </Card>
      {/* <Modal
        key={product.id}
        title="Scan With"
        visible={modal}
        onCancel={handleCancel}
        footer={[<Button onClick={handleCancel}>Cancel</Button>]}
        centered
      >
        <Row justify="center">
          <Col>
            <p>Barcode</p>
          </Col>
        </Row>
        <Row justify="center">
          <Col>
            <input type="text" onChange={() => setIsScanning(true)} ref={barcodeInputRef} />
          </Col>
        </Row>
        <Gaps />
        <Row justify="center">
          <Col>
            <p>Quantity/数</p>
          </Col>
        </Row>
        <Row justify="center">
          <Col>
            <input
              type="number"
              ref={qtyInputRef}
              max={qty - qtyScanned > 0 ? qty - qtyScanned : 1}
              min={1}
              defaultValue={1}
            />
          </Col>
        </Row>
        <Gaps />
 
        <Row justify="center">
          <Col>
            <p>Please scan the product barcode</p>
          </Col>
        </Row>
        <Row justify="center">
          <Col>
            <ScanOutlined style={{ fontSize: 50 }} />
          </Col>
        </Row>
        
      </Modal> */}
    </div>
  )
}
export default OrderScanning
