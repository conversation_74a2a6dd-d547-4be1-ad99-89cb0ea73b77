import React from 'react'
import { useNavigate } from 'react-router-dom'
import { Card, PageHeader, Button, Empty, Spin } from 'antd'
import useSWR from 'swr'
import { useSnapshot } from 'valtio'

import tableStore from '../../lib/store/source_table'
import SourceService from '../../network/services/source'
import { serialize } from '../../network/request'
import SourceTable from './SourceTable'

const SourcePage = () => {
  const navigate = useNavigate()
  const primaryAction = (
    <Button
      key="add"
      type="primary"
      onClick={() => {
        navigate('new')
      }}
    >
      Create Source
    </Button>
  )

  return (
    <PageHeader title="All Sources" extra={[primaryAction]}>
      <Card>
        <SourceTableWrapper />
      </Card>
    </PageHeader>
  )
}

const SourceTableWrapper = () => {
  const { state } = useSnapshot(tableStore)

  // use this to preload
  const { data: response, error } = useSWR(
    serialize(SourceService.getAll, { page: 1, limit: state.pageSize })
  )

  if (error) {
    console.log(error)
    return <Empty description={error?.message ?? 'Please try again later'} />
  }

  if (!response) {
    return <Spin />
  }

  const { total } = SourceService.toPaginate(response)

  return (
    <>
      <SourceTable total={total} />
      {/* preload next page */}
      {state.currentPage * state.pageSize < total && (
        <div style={{ display: 'none' }}>
          <SourceTable total={total} overridePage={state.currentPage + 1} />
        </div>
      )}
    </>
  )
}

export default SourcePage
