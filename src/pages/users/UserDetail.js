import React from 'react'
import { useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { Form, Empty, Spin, PageHeader, Button } from 'antd'
import useSWR from 'swr'

import { serialize } from '../../network/request'
import UserService from '../../network/services/user'
import UserForm from './UserForm'

const UserDetail = () => {
  const navigate = useNavigate()
  const { id } = useParams()

  const [form] = Form.useForm()
  const [isLoading, setIsLoading] = useState(false)

  const saveAction = (
    <Button
      key={'save'}
      type="primary"
      onClick={() => {
        form.submit()
      }}
      loading={isLoading}
    >
      Save
    </Button>
  )
  const discardAction = (
    <Button
      key={'discard'}
      type="ghost"
      onClick={() => {
        navigate('/dashboard/users')
      }}
    >
      Discard
    </Button>
  )

  // use this to preload
  const {
    data: response,
    error,
    mutate
  } = useSWR(serialize(id !== 'new' ? UserService.get(id) : null))

  var user = {
    ...response
  }

  if (response?.admin != null) {
    user = {
      ...response,
      ...response.admin
    }
  }

  if (error) {
    console.log(error)
    return <Empty description={error?.message ?? 'Please try again later'} />
  }

  if (!response && id !== 'new') {
    return (
      <PageHeader>
        <Spin />
      </PageHeader>
    )
  }

  const refresh = () => {
    if (id != null) {
      if (id === 'new') {
      } else {
        mutate()
      }
    }
  }

  return (
    <PageHeader
      title={id === 'new' ? 'New User' : 'View User'}
      extra={[discardAction, saveAction]}
      onBack={() => navigate('/dashboard/users')}
    >
      <UserForm
        form={form}
        initialValue={user}
        isLoading={isLoading}
        setIsLoading={setIsLoading}
        refresh={refresh}
      />
    </PageHeader>
  )
}

export default UserDetail
