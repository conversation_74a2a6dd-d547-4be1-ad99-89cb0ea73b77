import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>, Empty, Spin } from 'antd'
import { useNavigate } from 'react-router-dom'
import useSWR from 'swr'
import { useSnapshot } from 'valtio'

import tableStore from '../../lib/store/customer_table'
import CustomerService from '../../network/services/customer'
import { serialize } from '../../network/request'
import CustomerTable from './CustomerTable'

const CustomerPage = () => {
  const navigate = useNavigate()
  const primaryAction = (
    <Button
      key="add"
      type="primary"
      onClick={() => {
        navigate('new')
      }}
    >
      Create Customer
    </Button>
  )
  return (
    <PageHeader title="All Customer" extra={[primaryAction]}>
      <Card>
        <CustomerTableWrapper />
      </Card>
    </PageHeader>
  )
}

const CustomerTableWrapper = () => {
  const { state } = useSnapshot(tableStore)

  // use this to preload
  const { data: response, error } = useSWR(
    serialize(CustomerService.getAll, { page: 1, limit: state.pageSize })
  )

  if (error) {
    console.log(error)
    return <Empty description={error?.message ?? 'Please try again later'} />
  }

  if (!response) {
    return <Spin />
  }

  const { total } = CustomerService.toPaginate(response)

  return (
    <>
      <CustomerTable total={total} />
      {/* preload next page */}
      {state.currentPage * state.pageSize < total && (
        <div style={{ display: 'none' }}>
          <CustomerTable total={total} overridePage={state.currentPage + 1} />
        </div>
      )}
    </>
  )
}

export default CustomerPage
