{"name": "gaincue-admin-new", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^4.7.0", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@emotion/react": "^11.9.3", "@mdx-js/react": "1", "@react-three/drei": "^7.27.1", "@react-three/fiber": "^7.0.21", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "antd": "4.20.0", "array-move": "^4.0.0", "axios": "^0.24.0", "comma-number": "^2.1.0", "craco": "^0.0.3", "craco-less": "^1.20.0", "file-saver": "^2.0.5", "framer": "^1.3.6", "framer-motion": "4.1.17", "i18next": "^21.8.16", "i18next-browser-languagedetector": "^6.1.4", "i18next-http-backend": "^1.4.1", "immutable": "^4.0.0", "lodash": "^4.17.21", "luxon": "^2.3.0", "moment": "^2.29.3", "motion": "^10.4.0", "qs": "^6.10.2", "react": "^17.0.2", "react-barcode": "^1.4.1", "react-color": "^2.19.3", "react-dom": "^17.0.2", "react-i18next": "^11.18.3", "react-json-view": "^1.21.3", "react-player": "^2.9.0", "react-qr-barcode-scanner": "^1.0.6", "react-quill": "^1.3.5", "react-responsive": "^9.0.0-beta.5", "react-router-dom": "^6.0.2", "react-scripts": "4.0.3", "react-slider": "^1.3.1", "react-structured-data": "^0.0.14", "react-use": "^17.3.1", "styled-components": "^5.3.3", "swiper": "^8.3.2", "swr": "^1.1.0", "theme-ui": "^0.14.6", "three": "^0.135.0", "valtio": "^1.2.7", "web-vitals": "^1.0.1"}, "scripts": {"start": "craco --openssl-legacy-provider  start", "build": "craco --openssl-legacy-provider build", "test": "craco test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "resolutions": {"react-error-overlay": "6.0.9"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}